# Arbitrage Bot Configuration
bot:
  name: "Crypto Arbitrage Bot"
  version: "1.0.0"
  monitoring_interval: 5 # seconds
  profit_threshold: 0.02 # minimum profit percentage
  max_signal_frequency: 60 # seconds between same pair signals

# Trading pairs to monitor
trading_pairs:
  - symbol: "ADA/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "LABUBU/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "SHM/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "SQD/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "ESPORTS/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "LAUNCHCOIN/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "MORI/USDT"
    enabled: true
    min_profit: 0.02

# Exchange configuration
exchanges:
  mexc:
    enabled: true
    rate_limit: 1200
    timeout: 10
    commission_rate: 0.001 # 0.1% trading fee
    position_limit: 1000 # USD limit for positions
    api_urls:
      - "https://api.mexc.com/api/v3"
      - "https://api.mexc.global"
  gate:
    enabled: true
    rate_limit: 900
    timeout: 10
    commission_rate: 0.0007 # 0.07% trading fee
    position_limit: 1500 # USD limit for positions
    api_urls:
      - "https://api.gateio.ws"
      - "https://data.gate.io"
  lbank:
    enabled: true
    rate_limit: 600
    timeout: 10
    commission_rate: 0.001 # 0.1% trading fee
    position_limit: 800 # USD limit for positions
    api_urls:
      - "https://api.lbank.info"
      - "https://api.lbank.me"
  # mock_exchange_1:
  #   enabled: true
  #   type: mock
  # mock_exchange_2:
  #   enabled: true
  #   type: mock

# Database settings
database:
  path: "data/arbitrage.db"
  backup_interval: 3600 # seconds
  cleanup_days: 30

# Logging configuration
logging:
  level: "INFO"
  file_path: "logs/arbitrage.log"
  max_file_size: "10MB"
  backup_count: 5
  console_output: true

# Telegram settings (set via environment variables)
telegram:
  enabled: true
  parse_mode: "HTML"
  disable_web_page_preview: true
  message_template: |
    📗|{buy_exchange}| - UZUN
    Mevcut: %{buy_current_profit}
    Sapma: %{buy_deviation}
    🕐Dönem: {period}

    📕|{sell_exchange}| - KISA
    Mevcut: %{sell_current_profit}
    Sapma: %{sell_deviation}
    ⛔️Limit: {sell_limit}$
    🕐Dönem: {period}

    💰Kur farkı:
    Mevcut: %{profit_percent}
    Kur oranı: %{exchange_rate} ({buy_price} | {sell_price})
    (Komisyon: %{commission_rate})

    📈Grafik
